import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';

class StackedBar<PERSON>hart extends StatefulWidget {
  final List<Map<String, dynamic>> chartData;
  final String title;

  const StackedBarChart({
    Key? key,
    required this.chartData,
    required this.title,
  }) : super(key: key);

  @override
  State<StackedBarChart> createState() => _StackedBarChartState();
}

class _StackedBarChartState extends State<StackedBarChart> {
  String? selectedTooltip;

  @override
  Widget build(BuildContext context) {
    if (widget.chartData.isEmpty) {
      return const Center(
        child: Text(
          'No data available',
          style: TextStyle(color: Colors.grey),
        ),
      );
    }

    try {
      // Clean, vibrant color palette for age groups
      final Map<String, Color> ageGroupColors = {
        'total': const Color(0xFF4CAF50), // Green
        '16-24': const Color(0xFF6C5CE7), // Purple
        '25-34': const Color(0xFF00B894), // Teal
        '35-44': const Color(0xFFE17055), // Coral
        '45-54': const Color(0xFF0984E3), // Blue
        '55-64': const Color(0xFFFD79A8), // Pink
        '65-74': const Color(0xFF00CEC9), // Turquoise
        '75+': const Color(0xFFFFB8B8), // Light Pink
      };

      // Extract time labels
      List<String> timeLabels = [];
      for (var item in widget.chartData) {
        timeLabels.add(item['label'] as String);
      }

      // Create a legend for the chart
      Widget buildLegend() {
        // Get all age groups from the data
        Set<String> allAgeGroups = {};
        for (var item in widget.chartData) {
          final ageData = item['ageData'] as Map<String, dynamic>?;
          if (ageData != null) {
            allAgeGroups.addAll(ageData.keys.cast<String>());
          }
        }

        // Filter out 'total' and sort the age groups
        final filteredAgeGroups =
            allAgeGroups.where((age) => age != 'total').toList()..sort();

        // Create clean legend items
        final List<Widget> legendItems = [];
        for (var age in filteredAgeGroups) {
          legendItems.add(
            Container(
              padding:
                  const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
              margin: const EdgeInsets.symmetric(horizontal: 4.0),
              decoration: BoxDecoration(
                color: (ageGroupColors[age] ?? Colors.grey).withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: ageGroupColors[age] ?? Colors.grey,
                  width: 1,
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color: ageGroupColors[age] ?? Colors.grey,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 6),
                  Text(
                    age,
                    style: TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).textTheme.bodyMedium?.color,
                    ),
                  ),
                ],
              ),
            ),
          );
        }

        return Container(
          margin: const EdgeInsets.only(bottom: 16.0),
          child: Wrap(
            alignment: WrapAlignment.center,
            spacing: 8,
            runSpacing: 8,
            children: legendItems,
          ),
        );
      }

      return Column(
        children: [
          // Title
          Padding(
            padding: const EdgeInsets.only(bottom: 16.0),
            child: Text(
              'Unique Views by Time of Day and Age',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).textTheme.headlineSmall?.color,
              ),
            ),
          ),

          // Legend
          buildLegend(),

          // Tooltip display
          if (selectedTooltip != null)
            Container(
              margin: const EdgeInsets.only(bottom: 8.0),
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: const Color(0xFF4A148C),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                selectedTooltip!,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),

          // Chart
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: BarChart(
                BarChartData(
                  alignment: BarChartAlignment.spaceAround,
                  maxY: _getMaxY(),
                  minY: 0,
                  barTouchData: BarTouchData(
                    enabled: true,
                    touchCallback: (FlTouchEvent event, barTouchResponse) {
                      if (event is FlTapUpEvent && barTouchResponse != null) {
                        final spot = barTouchResponse.spot;
                        if (spot != null) {
                          _handleBarTap(spot.touchedBarGroupIndex);
                        }
                      }
                    },
                  ),
                  titlesData: FlTitlesData(
                    show: true,
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        getTitlesWidget: (double value, TitleMeta meta) {
                          final timeLabels = [
                            'Early Morning',
                            'Morning',
                            'Afternoon',
                            'Evening'
                          ];
                          if (value.toInt() >= 0 &&
                              value.toInt() < timeLabels.length) {
                            return Padding(
                              padding: const EdgeInsets.only(top: 8.0),
                              child: Text(
                                timeLabels[value.toInt()],
                                style: TextStyle(
                                  fontSize: 10,
                                  color: Theme.of(context)
                                      .textTheme
                                      .bodySmall
                                      ?.color,
                                ),
                              ),
                            );
                          }
                          return const Text('');
                        },
                        reservedSize: 20,
                      ),
                    ),
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        reservedSize: 30,
                        interval: 5000,
                        getTitlesWidget: (double value, TitleMeta meta) {
                          if (value % 5000 == 0) {
                            return Text(
                              '${(value / 1000).toInt()}K',
                              style: TextStyle(
                                fontSize: 10,
                                color: Theme.of(context)
                                    .textTheme
                                    .bodySmall
                                    ?.color,
                              ),
                            );
                          }
                          return const Text('');
                        },
                      ),
                    ),
                    topTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    rightTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                  ),
                  gridData: FlGridData(
                    show: true,
                    drawHorizontalLine: true,
                    drawVerticalLine: false,
                    horizontalInterval: 5000,
                    getDrawingHorizontalLine: (value) {
                      return FlLine(
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Colors.grey.shade700.withOpacity(0.3)
                            : Colors.grey.shade300.withOpacity(0.5),
                        strokeWidth: 1,
                      );
                    },
                  ),
                  borderData: FlBorderData(
                    show: true,
                    border: Border(
                      bottom: BorderSide(
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Colors.grey.shade600
                            : Colors.grey.shade300,
                        width: 1,
                      ),
                      left: BorderSide(
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Colors.grey.shade600
                            : Colors.grey.shade300,
                        width: 1,
                      ),
                    ),
                  ),
                  barGroups: _buildBarGroups(ageGroupColors),
                ),
              ),
            ),
          ),
        ],
      );
    } catch (e) {
      return Center(
        child: Text(
          'Error rendering chart: $e',
          style: TextStyle(color: Colors.red.shade700),
        ),
      );
    }
  }

  double _getMaxY() {
    double maxY = 0;
    for (var item in widget.chartData) {
      final y = (item['y'] ?? 0) as num;
      if (y > maxY) maxY = y.toDouble();
    }
    return maxY > 0 ? maxY * 1.1 : 15000; // Add 10% padding or default
  }

  List<BarChartGroupData> _buildBarGroups(Map<String, Color> ageGroupColors) {
    return List.generate(widget.chartData.length, (index) {
      final item = widget.chartData[index];
      final ageData = item['ageData'] as Map<String, dynamic>?;

      List<BarChartRodStackItem> stackItems = [];
      double fromY = 0;

      if (ageData != null && ageData.isNotEmpty) {
        final sortedAges = ageData.keys.where((age) => age != 'total').toList()
          ..sort();

        for (var age in sortedAges) {
          final value = ageData[age] ?? 0;
          if (value is num && value > 0) {
            final toY = fromY + value.toDouble();
            stackItems.add(
              BarChartRodStackItem(
                fromY,
                toY,
                ageGroupColors[age] ?? Colors.grey,
              ),
            );
            fromY = toY;
          }
        }
      }

      return BarChartGroupData(
        x: index,
        barRods: [
          BarChartRodData(
            toY: item['y']?.toDouble() ?? 0,
            color: Colors.transparent,
            width: 50,
            borderRadius: const BorderRadius.vertical(
              top: Radius.circular(4),
            ),
            rodStackItems: stackItems,
          ),
        ],
      );
    });
  }

  void _handleBarTap(int barIndex) {
    if (barIndex >= 0 && barIndex < widget.chartData.length) {
      final item = widget.chartData[barIndex];
      final ageData = item['ageData'] as Map<String, dynamic>?;
      final timeLabels = ['Early Morning', 'Morning', 'Afternoon', 'Evening'];
      final timeLabel =
          barIndex < timeLabels.length ? timeLabels[barIndex] : 'Unknown';

      if (ageData != null) {
        final total =
            ageData.values.fold<num>(0, (sum, value) => sum + (value ?? 0));
        setState(() {
          selectedTooltip =
              '$timeLabel\nUnique Views by Day of Week and Age: ${total.toInt()}';
        });

        // Auto-hide tooltip after 3 seconds
        Future.delayed(const Duration(seconds: 3), () {
          if (mounted) {
            setState(() {
              selectedTooltip = null;
            });
          }
        });
      }
    }
  }
}
