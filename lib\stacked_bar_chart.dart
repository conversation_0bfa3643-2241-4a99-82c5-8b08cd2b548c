import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';

class StackedBar<PERSON>hart extends StatelessWidget {
  final List<Map<String, dynamic>> chartData;
  final String title;

  const StackedBarChart({
    Key? key,
    required this.chartData,
    required this.title,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (chartData.isEmpty) {
      return const Center(
        child: Text(
          'No data available',
          style: TextStyle(color: Colors.grey),
        ),
      );
    }

    try {
      // Define colors for age groups to match the image exactly
      final Map<String, Color> ageGroupColors = {
        'total': const Color(0xFF4CAF50), // Green
        '16-24': const Color(0xFFFFEB3B), // Yellow
        '25-34': const Color(0xFF2E7D32), // Dark Green
        '35-44': const Color(0xFFFF80AB), // Pink
        '45-54': const Color(0xFF3F51B5), // Indigo
        '55-64': const Color(0xFF00BCD4), // <PERSON><PERSON>
        '65-74': const Color(0xFF4CAF50), // Green
        '75+': const Color(0xFF3F51B5), // Indigo
      };

      // Extract time labels
      List<String> timeLabels = [];
      for (var item in chartData) {
        timeLabels.add(item['label'] as String);
      }

      // Create a legend for the chart
      Widget buildLegend() {
        // Get all age groups from the data
        Set<String> allAgeGroups = {};
        for (var item in chartData) {
          final ageData = item['ageData'] as Map<String, dynamic>?;
          if (ageData != null) {
            allAgeGroups.addAll(ageData.keys.cast<String>());
          }
        }

        // Filter out 'total' and sort the age groups
        final filteredAgeGroups =
            allAgeGroups.where((age) => age != 'total').toList()..sort();

        // Create legend items
        final List<Widget> legendItems = [];
        for (var age in filteredAgeGroups) {
          legendItems.add(
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 6.0),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color: ageGroupColors[age] ?? Colors.grey,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    age,
                    style: const TextStyle(
                      fontSize: 8,
                      fontWeight: FontWeight.normal,
                      color: Colors.black87,
                    ),
                  ),
                ],
              ),
            ),
          );
        }

        // Arrange legend items in rows if needed
        return Padding(
          padding: const EdgeInsets.only(top: 4.0, bottom: 8.0),
          child: Wrap(
            alignment: WrapAlignment.center,
            spacing: 8,
            runSpacing: 4,
            children: legendItems,
          ),
        );
      }

      // Create a custom painter to draw value labels on the bars
      Widget buildValueLabels() {
        return Positioned.fill(
          child: CustomPaint(
            painter: _BarValuesPainter(chartData, ageGroupColors),
          ),
        );
      }

      return Padding(
        padding: const EdgeInsets.all(0),
        child: Column(
          children: [
            // Title
            const Padding(
              padding: EdgeInsets.only(top: 8.0, bottom: 4.0),
              child: Text(
                'Unique Views by Time of Day and Age',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
            ),

            // Legend
            buildLegend(),

            // Chart
            Expanded(
              child: Stack(
                children: [
                  // Bar chart
                  Padding(
                    padding: const EdgeInsets.fromLTRB(8, 0, 8, 24),
                    child: BarChart(
                      BarChartData(
                        alignment: BarChartAlignment.spaceAround,
                        maxY: 15000,
                        minY: 0,
                        barTouchData: BarTouchData(
                          enabled: true,
                          touchTooltipData: BarTouchTooltipData(
                            tooltipBgColor: Colors.black87,
                            tooltipRoundedRadius: 4,
                            tooltipPadding: const EdgeInsets.all(8),
                            tooltipMargin: 8,
                            getTooltipItem: (group, groupIndex, rod, rodIndex) {
                              // Get the item data
                              final item = chartData[groupIndex];
                              final ageData =
                                  item['ageData'] as Map<String, dynamic>?;

                              if (ageData != null && ageData.isNotEmpty) {
                                // Sort age groups
                                final sortedAges = ageData.keys.toList()
                                  ..sort();

                                // Make sure we have a valid index
                                if (rodIndex >= 0 &&
                                    rodIndex < sortedAges.length) {
                                  // Get the specific age group that was touched
                                  final touchedAge = sortedAges[rodIndex];
                                  final value = ageData[touchedAge] ?? 0;

                                  return BarTooltipItem(
                                    '$touchedAge: ${value is num ? value.toInt() : value}',
                                    const TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 10,
                                    ),
                                  );
                                }
                              }
                              return null;
                            },
                            fitInsideHorizontally: true,
                            fitInsideVertically: true,
                          ),
                        ),
                        titlesData: FlTitlesData(
                          show: true,
                          bottomTitles: AxisTitles(
                            sideTitles: SideTitles(
                              showTitles: true,
                              getTitlesWidget: (value, meta) {
                                final index = value.toInt();
                                if (index >= 0 && index < timeLabels.length) {
                                  return Padding(
                                    padding: const EdgeInsets.only(top: 8.0),
                                    child: Text(
                                      timeLabels[index]
                                          .replaceAll(RegExp(r'^\d+\.\s*'), ''),
                                      style: const TextStyle(
                                        fontSize: 8,
                                        color: Colors.black,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                  );
                                }
                                return const SizedBox.shrink();
                              },
                              reservedSize: 20,
                            ),
                          ),
                          leftTitles: AxisTitles(
                            sideTitles: SideTitles(
                              showTitles: true,
                              reservedSize: 30,
                              interval: 5000,
                              getTitlesWidget: (value, meta) {
                                if (value % 5000 == 0) {
                                  return Text(
                                    value.toInt().toString(),
                                    style: const TextStyle(
                                      fontSize: 8,
                                      color: Colors.grey,
                                    ),
                                  );
                                }
                                return const SizedBox.shrink();
                              },
                            ),
                          ),
                          topTitles: const AxisTitles(
                            sideTitles: SideTitles(showTitles: false),
                          ),
                          rightTitles: const AxisTitles(
                            sideTitles: SideTitles(showTitles: false),
                          ),
                        ),
                        gridData: FlGridData(
                          show: true,
                          drawHorizontalLine: true,
                          drawVerticalLine: false,
                          horizontalInterval: 5000,
                          getDrawingHorizontalLine: (value) {
                            if (value % 5000 == 0 && value > 0) {
                              return FlLine(
                                color: Colors.grey.shade200,
                                strokeWidth: 0.5,
                              );
                            }
                            return const FlLine(
                              color: Colors.transparent,
                              strokeWidth: 0,
                            );
                          },
                        ),
                        borderData: FlBorderData(
                          show: true,
                          border: Border(
                            bottom: BorderSide(
                                color: Colors.grey.shade300, width: 1),
                            left: BorderSide(
                                color: Colors.grey.shade300, width: 1),
                          ),
                        ),
                        barGroups: List.generate(
                          chartData.length,
                          (index) {
                            final item = chartData[index];
                            final x = (item['x'] ?? 0) as int;
                            final ageData =
                                item['ageData'] as Map<String, dynamic>?;

                            // Create stacked bar rods for each age group
                            List<BarChartRodStackItem> stackItems = [];
                            double fromY = 0;

                            // If we have age data, create stacked bars
                            if (ageData != null && ageData.isNotEmpty) {
                              // Sort age groups
                              final sortedAges = ageData.keys.toList()..sort();

                              // Create stack items for each age group
                              for (var age in sortedAges) {
                                final value = ageData[age] ?? 0;
                                if (value is num && value > 0) {
                                  final toY = fromY + value.toDouble();
                                  stackItems.add(
                                    BarChartRodStackItem(
                                      fromY,
                                      toY,
                                      ageGroupColors[age] ?? Colors.grey,
                                    ),
                                  );
                                  fromY = toY;
                                }
                              }
                            }

                            return BarChartGroupData(
                              x: x,
                              barRods: [
                                BarChartRodData(
                                  toY: item['y'] ?? 0,
                                  color: Colors.transparent,
                                  width: 30, // Wider bars to match the image
                                  borderRadius: const BorderRadius.vertical(
                                    top: Radius.circular(6),
                                    bottom: Radius.circular(0),
                                  ),
                                  rodStackItems: stackItems,
                                ),
                              ],
                            );
                          },
                        ),
                      ),
                    ),
                  ),

                  // Custom painter for value labels
                  buildValueLabels(),
                ],
              ),
            ),
          ],
        ),
      );
    } catch (e) {
      return Center(
        child: Text(
          'Error rendering chart: $e',
          style: TextStyle(color: Colors.red.shade700),
        ),
      );
    }
  }
}

class _BarValuesPainter extends CustomPainter {
  final List<Map<String, dynamic>> chartData;
  final Map<String, Color> ageGroupColors;

  _BarValuesPainter(this.chartData, this.ageGroupColors);

  @override
  void paint(Canvas canvas, Size size) {
    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.center,
    );

    // Calculate the actual chart area (accounting for padding)
    final double chartWidth = size.width - 16; // 8px padding on each side
    final double chartHeight = size.height - 24; // 24px bottom padding
    const double barWidth = 30;
    final int barCount = chartData.length;
    final double totalBarsWidth = barWidth * barCount;
    final double totalSpacing = chartWidth - totalBarsWidth;
    final double spacing = totalSpacing / (barCount + 1);

    // Get the maximum Y value from the actual data
    double maxY = 0;
    for (var item in chartData) {
      final y = (item['y'] ?? 0) as num;
      if (y > maxY) maxY = y.toDouble();
    }
    if (maxY == 0) maxY = 15000; // fallback

    for (int i = 0; i < chartData.length; i++) {
      final item = chartData[i];
      final ageData = item['ageData'] as Map<String, dynamic>?;
      if (ageData == null || ageData.isEmpty) continue;

      // Sort age groups
      final sortedAges = ageData.keys.toList()..sort();

      // Calculate bar position (accounting for padding)
      final double barLeft =
          8 + spacing + i * (barWidth + spacing); // 8px left padding
      double currentY = chartHeight; // Start from bottom of chart area

      // Draw values for each section from bottom to top
      for (var age in sortedAges) {
        final value = ageData[age] ?? 0;
        if (value is! num || value <= 0) continue;

        // Calculate section height based on actual chart proportions
        final sectionHeight = (value / maxY) * chartHeight;
        currentY -= sectionHeight;

        // Only draw text if section is large enough
        if (sectionHeight > 12) {
          // Minimum height for text visibility
          // Draw value text
          textPainter.text = TextSpan(
            text: value.toInt().toString(),
            style: const TextStyle(
              color: Colors.white,
              fontSize: 8,
              fontWeight: FontWeight.bold,
              shadows: [
                Shadow(
                  offset: Offset(0.5, 0.5),
                  blurRadius: 1.0,
                  color: Colors.black54,
                ),
              ],
            ),
          );

          textPainter.layout(minWidth: 0, maxWidth: barWidth);

          // Position text in the middle of the section
          final textX = barLeft + (barWidth - textPainter.width) / 2;
          final textY = currentY + (sectionHeight - textPainter.height) / 2;

          // Ensure text is within bounds
          if (textY >= 0 && textY + textPainter.height <= size.height) {
            textPainter.paint(canvas, Offset(textX, textY));
          }
        }
      }
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => true;
}
