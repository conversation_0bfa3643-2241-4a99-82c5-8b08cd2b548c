import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/material.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'login_page.dart';

class AuthService {
  final String apiUrl =
      'http://********:5168/Auth'; // replace ******** with your backend IP if needed
  final _storage = const FlutterSecureStorage();

  Future<bool> login(String username, String password) async {
    final response = await http.post(
      Uri.parse('$apiUrl/login'),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({'username': username, 'password': password}),
    );

    print('Status code: ${response.statusCode}');
    print('Response body: ${response.body}');

    if (response.statusCode == 200) {
      final json = jsonDecode(response.body);
      if (json['token'] != null) {
        await _storage.write(key: 'token', value: json['token']);
        return true;
      }
    }
    return false;
  }

  Future<void> logout() async {
    try {
      await _storage.delete(key: 'token');
      print('Token deleted successfully');
    } catch (e) {
      print('Error during logout: $e');
    }
  }

  // New method to handle logout with navigation
  static void logoutAndNavigate(BuildContext context) async {
    // Create an instance of AuthService
    final authService = AuthService();

    // Delete the token
    await authService.logout();

    // Navigate to login page
    if (context.mounted) {
      // Clear all routes and navigate to login page
      Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(builder: (context) => const LoginPage()),
        (route) => false,
      );
    }
  }

  Future<String?> getToken() async {
    return await _storage.read(key: 'token');
  }

  Future<bool> isLoggedIn() async {
    return (await getToken()) != null;
  }
}
