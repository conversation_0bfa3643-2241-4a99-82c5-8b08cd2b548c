// dashboard_service.dart
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:flutter/foundation.dart';

class DashboardService {
  final String baseUrl = 'http://10.0.2.2:5168/api/dashboard';
  final _storage = const FlutterSecureStorage();

  Future<String?> _getToken() async {
    return await _storage.read(key: 'token');
  }

  Future<dynamic> _get(String endpoint) async {
    try {
      final token = await _getToken();
      if (token == null) {
        throw Exception('No JWT token found. Please login first.');
      }

      final response = await http.get(
        Uri.parse('$baseUrl/$endpoint'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      ).timeout(const Duration(seconds: 10), onTimeout: () {
        throw Exception('Request timed out. Check your network connection.');
      });

      if (response.statusCode == 200) {
        // Try to parse the response body
        dynamic data;
        try {
          data = jsonDecode(response.body);
          debugPrint('API Response for $endpoint: $data');
        } catch (e) {
          debugPrint('Error parsing JSON for $endpoint: $e');
          throw Exception('Failed to parse API response: $e');
        }

        // If the data is empty, throw an exception
        if (data == null) {
          debugPrint('Null data received for $endpoint');
          throw Exception('No data received from API');
        }

        // If data is an empty list or map, return empty data
        if ((data is List && data.isEmpty) || (data is Map && data.isEmpty)) {
          debugPrint('Empty data received for $endpoint');
          return data; // Return empty data as is
        }

        // Return the data as is - let the transformation function in the UI handle it
        return data;
      } else if (response.statusCode == 401) {
        throw Exception('Unauthorized. Token may be expired.');
      } else {
        throw Exception('Failed to load $endpoint: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error in _get for $endpoint: $e');
      // Rethrow the exception to be handled by the UI
      throw Exception('Failed to fetch data: $e');
    }
  }

  Future<dynamic> getCampaignStats() => _get('campaign-stats');
  Future<dynamic> getSpendByGender() => _get('SpendByGender');
  Future<dynamic> getTotalSpend() => _get('TotalSpend');
  Future<dynamic> getNbSent() => _get('NbSent');
  Future<dynamic> getTotalNbDeliv() => _get('TotalNbDeliv');
  Future<dynamic> getDeliveryRate() => _get('DeliveryRate');
  Future<dynamic> getOpenRate() => _get('OpenRate');
  Future<dynamic> getVisited() => _get('Visited');
  Future<dynamic> getOpenRateByCampaign() => _get('OpenRateByCampaign');
  Future<dynamic> getDeliveryRateByGender() => _get('DeliveryRateByGender');
  Future<dynamic> getNbSpendByAge() => _get('NbSpendByAge');
  Future<dynamic> getTODEOperAgeandDOWEO() => _get('TODEOperAgeandDOWEO');
  Future<dynamic> getTODEOAGE() => _get('TODEOAGE');
}
