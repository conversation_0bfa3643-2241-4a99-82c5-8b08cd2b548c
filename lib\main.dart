import 'package:flutter/material.dart';
import 'login_page.dart';
import 'dashboard_page1.dart';
import 'dashboard_page2.dart';

void main() {
  WidgetsFlutterBinding.ensureInitialized();
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'CoMark App',
      theme: ThemeData(primarySwatch: Colors.blue),
      debugShowCheckedModeBanner: false, // Remove debug banner
      initialRoute: '/',
      routes: {
        '/': (context) => const LoginPage(),
        '/dashboard1': (context) => const DashboardPage1(),
        '/dashboard2': (context) => const DashboardPage2(),
      },
    );
  }
}
