import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'splash_page.dart';
import 'login_page.dart';
import 'dashboard_page1.dart';
import 'dashboard_page2.dart';
import 'settings_page.dart';
import 'about_us_page.dart';
import 'theme_provider.dart';

void main() {
  WidgetsFlutterBinding.ensureInitialized();
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => ThemeProvider(),
      child: Consumer<ThemeProvider>(
        builder: (context, themeProvider, child) {
          return MaterialApp(
            title: 'CoMark App',
            theme: ThemeProvider.lightTheme,
            darkTheme: ThemeProvider.darkTheme,
            themeMode: themeProvider.themeMode,
            debugShowCheckedModeBanner: false, // Remove debug banner
            initialRoute: '/',
            routes: {
              '/': (context) => const SplashPage(),
              '/login': (context) => const LoginPage(),
              '/dashboard1': (context) => const DashboardPage1(),
              '/dashboard2': (context) => const DashboardPage2(),
              '/settings': (context) => const SettingsPage(),
              '/about': (context) => const AboutUsPage(),
            },
          );
        },
      ),
    );
  }
}
