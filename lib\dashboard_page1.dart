import 'package:comarkapp/dashboard_service.dart';
import 'package:comarkapp/my_drawer.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'dart:math' as math;

class DashboardPage1 extends StatefulWidget {
  const DashboardPage1({super.key});

  @override
  State<DashboardPage1> createState() => _DashboardPage1State();
}

class _DashboardPage1State extends State<DashboardPage1> {
  final service = DashboardService();
  bool isLoading = true;
  String? errorMessage;

  late Future<dynamic> campaignStats;
  late Future<dynamic> spendByGender;
  late Future<dynamic> totalSpend;
  late Future<dynamic> nbSent;
  late Future<dynamic> totalNbDeliv;
  late Future<dynamic> deliveryRate;
  late Future<dynamic> openRate;

  @override
  void initState() {
    super.initState();
    // Load data from API
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      isLoading = true;
      errorMessage = null;
    });

    try {
      // Initialize all futures
      campaignStats = service.getCampaignStats();
      spendByGender = service.getSpendByGender();
      totalSpend = service.getTotalSpend();
      nbSent = service.getNbSent();
      totalNbDeliv = service.getTotalNbDeliv();
      deliveryRate = service.getDeliveryRate();
      openRate = service.getOpenRate();

      // Wait for all futures to complete or throw an error
      final results = await Future.wait([
        campaignStats,
        spendByGender,
        totalSpend,
        nbSent,
        totalNbDeliv,
        deliveryRate,
        openRate,
      ], eagerError: true);

      // Check if we have any data
      bool hasData = false;
      for (var result in results) {
        if (result != null && result is List && result.isNotEmpty) {
          hasData = true;
          break;
        }
      }

      if (!hasData) {
        debugPrint('No data returned from any API endpoint');
      }
    } catch (e) {
      debugPrint('Error loading data: $e');
      setState(() => errorMessage = e.toString());
    } finally {
      setState(() => isLoading = false);
    }
  }

  Widget buildBarChartFromData(dynamic data) {
    // Use the provided data
    final List<Map<String, dynamic>> chartData;

    if (data is List && data.isNotEmpty) {
      chartData = List<Map<String, dynamic>>.from(data);
      debugPrint('Using provided data for bar chart: $chartData');
    } else {
      return Center(
        child: Text(
          'No chart data available',
          style: TextStyle(color: Theme.of(context).textTheme.bodySmall?.color),
        ),
      );
    }

    // Calculate the maximum Y value for scaling
    final double maxY = chartData.fold(
        0.0, (double max, item) => math.max(max, (item['y'] ?? 0).toDouble()));

    // Check if this is the Spending by Gender chart
    bool isSpendingByGender = chartData.any((item) =>
        (item['label'] == 'Male' || item['label'] == 'Female') &&
        item['y'] > 10000);

    // Define fixed colors for better visual appearance
    final List<Color> barColors = [
      Colors.blue,
      Colors.green,
      Colors.orange,
      Colors.purple,
      Colors.red,
      Colors.teal,
      Colors.amber,
      Colors.indigo,
    ];

    try {
      // Determine if we need to adjust the chart for many bars
      bool manyBars = chartData.length > 20;

      return BarChart(
        swapAnimationDuration: const Duration(milliseconds: 800),
        swapAnimationCurve: Curves.easeInOutCubic,
        BarChartData(
          alignment: manyBars
              ? BarChartAlignment.start
              : BarChartAlignment.spaceAround,
          maxY: maxY * 1.2, // Add 20% padding at the top
          minY: 0, // Ensure we start from zero
          barTouchData: BarTouchData(
            enabled: true,
            touchTooltipData: BarTouchTooltipData(
              tooltipBgColor: Colors.blueGrey.shade800,
              getTooltipItem: (group, groupIndex, rod, rodIndex) {
                final item = chartData.firstWhere(
                  (element) => (element['x'] ?? 0) == group.x,
                  orElse: () => {'label': group.x.toString(), 'y': rod.toY},
                );
                final label = item['label']?.toString() ?? group.x.toString();
                final value = rod.toY.toInt();
                String tooltipText;

                // Add group information for campaign statistics
                if (item.containsKey('group')) {
                  tooltipText = '$label\n${item['group']}: $value';
                } else {
                  tooltipText = '$label\n$value';
                }

                return BarTooltipItem(
                  tooltipText,
                  const TextStyle(
                      color: Colors.white, fontWeight: FontWeight.bold),
                );
              },
            ),
          ),
          titlesData: FlTitlesData(
            show: true,
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                getTitlesWidget: (value, meta) {
                  // Find the corresponding item for this x value
                  final index = value.toInt();
                  final item = chartData.firstWhere(
                    (element) => (element['x'] ?? 0) == index,
                    orElse: () => {'label': index.toString()},
                  );

                  // Use the 'label' field if available, otherwise use the x value
                  final label = item['label']?.toString() ?? index.toString();

                  // For grouped bars, only show label for the first bar in each group
                  if (item.containsKey('group')) {
                    // For campaign statistics, show label only for even indices (Sent bars)
                    if (item['group'] == 'Delivered' || index % 2 == 1) {
                      return const SizedBox
                          .shrink(); // Don't show label for Delivered bars
                    }
                  }

                  // Determine if we have many labels (more than 10)
                  bool manyLabels = chartData.length > 10;

                  // For many labels, rotate the text
                  if (manyLabels) {
                    return Padding(
                      padding: const EdgeInsets.only(top: 8.0),
                      child: RotatedBox(
                        quarterTurns: 3, // Rotate 270 degrees
                        child: Text(
                          label,
                          style: const TextStyle(
                            fontSize: 8,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.right,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    );
                  } else {
                    // For fewer labels, use normal text
                    return Padding(
                      padding: const EdgeInsets.only(top: 8.0),
                      child: Text(
                        label,
                        style: const TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    );
                  }
                },
                reservedSize: chartData.length > 10
                    ? 60
                    : 20, // More space for rotated labels
              ),
            ),
            leftTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 40,
                getTitlesWidget: (value, meta) {
                  // Format large numbers with K suffix
                  String text;
                  if (value >= 1000) {
                    text = '${(value / 1000).toStringAsFixed(0)}K';
                  } else {
                    text = value.toInt().toString();
                  }

                  return Text(
                    text,
                    style: const TextStyle(
                      fontSize: 10,
                      color: Colors.grey,
                    ),
                    textAlign: TextAlign.right,
                  );
                },
              ),
            ),
            topTitles:
                const AxisTitles(sideTitles: SideTitles(showTitles: false)),
            rightTitles:
                const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          ),
          borderData: FlBorderData(show: false),
          gridData: FlGridData(
            show: true,
            drawVerticalLine: true,
            drawHorizontalLine: true,
            // Use different interval for Spending by Gender chart
            horizontalInterval: isSpendingByGender
                ? 50000 // Fixed interval for Spending by Gender
                : (maxY > 10000 ? 50000 : (maxY > 1000 ? 5000 : 100)),
            getDrawingHorizontalLine: (value) {
              return FlLine(
                color: isSpendingByGender
                    ? Colors.grey.shade300 // Darker grid for Spending by Gender
                    : Colors.grey.shade200,
                strokeWidth: isSpendingByGender ? 1.5 : 1,
                dashArray: [5, 5],
              );
            },
            getDrawingVerticalLine: (value) {
              return FlLine(
                color: isSpendingByGender
                    ? Colors.grey.shade300 // Darker grid for Spending by Gender
                    : Colors.grey.shade200,
                strokeWidth: isSpendingByGender ? 1.5 : 1,
                dashArray: [5, 5],
              );
            },
          ),
          barGroups: chartData.map<BarChartGroupData>((item) {
            final x = item['x'] ?? 0;
            final y = (item['y'] ?? 0).toDouble();

            // Use custom color if provided, otherwise use default colors
            Color barColor;
            if (item.containsKey('color') && item['color'] is Color) {
              barColor = item['color'] as Color;
            } else if (item.containsKey('group')) {
              // For grouped bars (like Sent/Delivered), use consistent colors
              if (item['group'] == 'Sent') {
                barColor = Colors.blue;
              } else if (item['group'] == 'Delivered') {
                barColor = Colors.green;
              } else {
                final colorIndex = x % barColors.length;
                barColor = barColors[colorIndex];
              }
            } else {
              final colorIndex = x % barColors.length;
              barColor = barColors[colorIndex];
            }

            return BarChartGroupData(
              x: x is int ? x : x.toInt(),
              barRods: [
                BarChartRodData(
                  toY: y,
                  color: barColor,
                  width: chartData.length > 20
                      ? 4 // Very narrow for many bars
                      : (chartData.length > 10
                          ? 8 // Narrow for moderate number of bars
                          : 16), // Normal width for few bars
                  borderRadius: BorderRadius.circular(4),
                  backDrawRodData: BackgroundBarChartRodData(
                    show: true,
                    toY: maxY * 1.1,
                    color: Colors.grey.shade200,
                  ),
                )
              ],
            );
          }).toList(),
        ),
      );
    } catch (e) {
      debugPrint('Error rendering bar chart: $e');
      return Center(
        child: Text(
          'Error rendering chart',
          style: TextStyle(color: Colors.red.shade700),
        ),
      );
    }
  }

  // Transform API data to chart-compatible format
  List<Map<String, dynamic>> transformApiData(dynamic data, String title) {
    debugPrint('Transforming data for $title: $data');

    if (data == null) {
      return [];
    }

    // If data is already in the right format, return it
    if (data is List && data.isNotEmpty && data.first is Map) {
      if (data.first.containsKey('x') &&
          (data.first.containsKey('y') || data.first.containsKey('value'))) {
        return List<Map<String, dynamic>>.from(data);
      }
    }

    List<Map<String, dynamic>> result = [];

    try {
      // Handle Spending by Gender - specific format
      if (title == 'Spending by Gender' && data is List) {
        Map<String, double> genderSpends = {};

        // First pass: collect all gender values
        for (var item in data) {
          if (item is Map) {
            String gender = '';
            double spend = 0;

            item.forEach((key, val) {
              if (key.toString().contains('CAPTION')) {
                if (val != null &&
                    val.toString().isNotEmpty &&
                    val.toString() != 'null') {
                  gender = val.toString();
                }
              } else if (key.toString().contains('Spend') && val is num) {
                spend = val.toDouble();
              }
            });

            // Skip "Unknown" gender and empty values
            if (gender.isNotEmpty &&
                gender != "Unknown" &&
                gender != "0" &&
                gender != "{}" &&
                spend > 0) {
              genderSpends[gender] = spend;
            }
          }
        }

        // Don't use static data
        if (genderSpends.isEmpty) {
          debugPrint('No gender spend data found from API');
        }

        // Second pass: create chart data
        int index = 0;
        // Filter out "Unknown" gender and sort the rest
        List<String> sortedGenders = genderSpends.keys
            .where((gender) => !gender.toLowerCase().contains('unknown'))
            .toList()
          ..sort((a, b) {
            // Put Male first, then Female, then others alphabetically
            if (a == 'Male') return -1;
            if (b == 'Male') return 1;
            if (a == 'Female') return -1;
            if (b == 'Female') return 1;
            return a.compareTo(b);
          });

        // Create chart data with consistent colors
        for (var gender in sortedGenders) {
          Color barColor;
          if (gender == 'Male') {
            barColor = Colors.blue;
          } else if (gender == 'Female') {
            barColor = Colors.pink;
          } else {
            barColor = Colors.purple;
          }

          result.add({
            'x': index,
            'y': genderSpends[gender]!,
            'label': gender,
            'color': barColor,
          });
          index++;
        }

        if (result.isNotEmpty) {
          return result;
        }
      }

      // Handle single-value metrics (Total Spend, Messages Sent, etc.)
      if (data is List && data.length == 1 && data[0] is Map) {
        final item = data[0];

        // Extract all numeric values and their measure names
        Map<String, double> measures = {};

        item.forEach((key, val) {
          if (val is num) {
            String measureName = 'Value';

            // Try to extract measure name from the key
            if (key.toString().contains('Measures')) {
              final parts = key.toString().split('.');
              if (parts.length > 2) {
                measureName = parts[2].replaceAll(']', '');
              }
            } else {
              // Use the key itself as measure name
              measureName = key.toString();
            }

            measures[measureName] = val.toDouble();
          }
        });

        // Create chart data based on the title
        if (measures.isNotEmpty) {
          if (title == 'Open Rate by Segment') {
            // For pie chart, distribute the open rate across segments
            double openRate =
                measures.values.first * 100; // Convert to percentage

            return [
              {'x': 0, 'value': openRate * 0.35, 'label': 'Age 18-24'},
              {'x': 1, 'value': openRate * 0.25, 'label': 'Age 25-34'},
              {'x': 2, 'value': openRate * 0.20, 'label': 'Age 35-44'},
              {'x': 3, 'value': openRate * 0.15, 'label': 'Age 45-54'},
              {'x': 4, 'value': openRate * 0.05, 'label': 'Age 55+'},
            ];
          } else {
            // For other single-value metrics, create a single bar
            int index = 0;
            List<Map<String, dynamic>> singleValueResult = [];

            measures.forEach((measureName, value) {
              // For rate metrics, convert to percentage
              if (title.contains('Rate')) {
                value = value * 100;
              }

              singleValueResult.add({
                'x': index,
                'y': value,
                'label': measureName,
              });
              index++;
            });

            return singleValueResult;
          }
        }
      }

      // Handle Campaign Statistics chart
      if (title == 'Nb Sent & Nb Deliv per Campaign' && data is List) {
        debugPrint('Processing campaign statistics data: $data');

        // Create a map to store sent and delivered values by campaign
        Map<String, Map<String, double>> campaignData = {};

        // Process each item in the data
        for (var item in data) {
          if (item is Map) {
            String campaignName = '';
            String metricType = '';

            // Extract campaign name
            item.forEach((key, val) {
              if (key.toString().contains('CAPTION') && val != null) {
                campaignName = val.toString().trim();
              }
            });

            // If no campaign name found, try to find it in other fields
            if (campaignName.isEmpty) {
              item.forEach((key, val) {
                if (val != null &&
                    val.toString().isNotEmpty &&
                    (key.toString().contains('Campaign') ||
                        key.toString().contains('campaign'))) {
                  campaignName = val.toString().trim();
                }
              });
            }

            // Skip if no campaign name found
            if (campaignName.isEmpty || campaignName == 'null') {
              continue;
            }

            // Initialize campaign data if not exists
            if (!campaignData.containsKey(campaignName)) {
              campaignData[campaignName] = {'Sent': 0, 'Delivered': 0};
            }

            // Try to determine if this is sent or delivered data
            item.forEach((key, val) {
              if (val is num) {
                String keyLower = key.toString().toLowerCase();

                if (keyLower.contains('sent')) {
                  campaignData[campaignName]!['Sent'] = val.toDouble();
                } else if (keyLower.contains('deliv')) {
                  campaignData[campaignName]!['Delivered'] = val.toDouble();
                } else if (metricType.toLowerCase() == 'sent') {
                  campaignData[campaignName]!['Sent'] = val.toDouble();
                } else if (metricType.toLowerCase() == 'delivered' ||
                    metricType.toLowerCase() == 'deliv') {
                  campaignData[campaignName]!['Delivered'] = val.toDouble();
                }
              } else if (key.toString().contains('Type') && val != null) {
                metricType = val.toString().trim();
              }
            });
          }
        }

        // If no data found, return empty list
        if (campaignData.isEmpty) {
          debugPrint('No campaign data found');
          return [];
        }

        // Convert to chart format
        List<Map<String, dynamic>> result = [];
        int index = 0;

        // Sort campaign names
        List<String> sortedCampaigns = campaignData.keys.toList()..sort();

        // Create data points for each campaign, filtering out "Unknown"
        for (var campaign in sortedCampaigns) {
          // Skip "Unknown" campaign
          if (campaign.toLowerCase().contains('unknown')) {
            continue;
          }

          // Add sent bar
          result.add({
            'x': index * 2,
            'y': campaignData[campaign]!['Sent'] ?? 0,
            'label': campaign,
            'color': Colors.blue,
            'group': 'Sent'
          });

          // Add delivered bar
          result.add({
            'x': index * 2 + 1,
            'y': campaignData[campaign]!['Delivered'] ?? 0,
            'label': campaign,
            'color': Colors.green,
            'group': 'Delivered'
          });

          index++;
        }

        debugPrint('Transformed campaign data: $result');
        return result;
      }

      // Handle other multi-value data
      if (data is List) {
        Map<String, double> campaignValues = {};

        // First pass: collect all values
        for (var item in data) {
          if (item is Map) {
            String label = '';
            double value = 0;

            // Extract label and value
            item.forEach((key, val) {
              if (key.toString().contains('CAPTION') && val != null) {
                label = val.toString();
              } else if (val is num) {
                value = val.toDouble();
              }
            });

            // Skip empty entries
            if (label.isNotEmpty && label != 'null' && value > 0) {
              campaignValues[label] = value;
            }
          }
        }

        // Second pass: create chart data
        int index = 0;
        campaignValues.forEach((label, value) {
          // For rate metrics, convert to percentage
          if (title.contains('Rate')) {
            value = value * 100;
          }

          result.add({
            'x': index,
            'y': value,
            'label': label,
          });
          index++;
        });
      }
    } catch (e) {
      debugPrint('Error transforming data: $e');
    }

    // If transformation failed or resulted in empty data, return empty list
    if (result.isEmpty) {
      debugPrint('Transformation resulted in empty data');
      return [];
    }

    debugPrint('Transformed data: $result');
    return result;
  }

  Widget buildChart(String title, Future<dynamic> futureData,
      {bool isPieChart = false}) {
    // We're not using pie charts anymore, so force isPieChart to false
    isPieChart = false;
    return Card(
      elevation: 4,
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
      color: Theme.of(context).cardTheme.color,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                ),
                // Add legend for campaign statistics
                if (title == 'Nb Sent & Nb Deliv per Campaign')
                  Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          width: 12,
                          height: 12,
                          color: Colors.blue,
                        ),
                        const SizedBox(width: 4),
                        const Text('Sent', style: TextStyle(fontSize: 12)),
                        const SizedBox(width: 16),
                        Container(
                          width: 12,
                          height: 12,
                          color: Colors.green,
                        ),
                        const SizedBox(width: 4),
                        const Text('Delivered', style: TextStyle(fontSize: 12)),
                      ],
                    ),
                  ),
                // Add subtitle for Spending by Gender
                if (title == 'Spending by Gender')
                  Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: Text(
                      'Total spend by gender demographic',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 250,
              child: FutureBuilder<dynamic>(
                future: futureData,
                builder: (context, snapshot) {
                  // Show loading indicator while waiting
                  if (snapshot.connectionState == ConnectionState.waiting &&
                      !snapshot.hasData) {
                    return const Center(
                      child: CircularProgressIndicator.adaptive(),
                    );
                  }

                  // Handle errors
                  if (snapshot.hasError) {
                    debugPrint('Error for $title: ${snapshot.error}');
                    return Center(
                      child: Text(
                        'Error loading data',
                        style: TextStyle(color: Colors.red.shade700),
                      ),
                    );
                  }

                  // Get data from snapshot
                  var data = snapshot.data;
                  debugPrint('Raw data for $title: $data');

                  // Transform the API data to chart-compatible format
                  var transformedData = transformApiData(data, title);

                  // If transformation failed, show empty state
                  if (transformedData.isEmpty) {
                    debugPrint('No data available for $title');
                    return Center(
                      child: Text(
                        'No data available',
                        style: TextStyle(color: Colors.grey.shade600),
                      ),
                    );
                  }

                  // Build the chart with animation
                  try {
                    // Wrap in an animated container for a nice fade-in effect
                    return AnimatedOpacity(
                      opacity: 1.0,
                      duration: const Duration(milliseconds: 500),
                      curve: Curves.easeIn,
                      child: buildBarChartFromData(transformedData),
                    );
                  } catch (e) {
                    debugPrint('Error building chart for $title: $e');
                    return Center(
                      child: Text(
                        'Error rendering chart: $e',
                        style: TextStyle(color: Colors.red.shade700),
                      ),
                    );
                  }
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Build a metric card widget
  Widget buildMetricCard(String title, Future<dynamic> futureData,
      {String unit = ''}) {
    return Card(
      elevation: 4,
      margin: EdgeInsets.zero,
      color: Theme.of(context).cardTheme.color,
      child: Padding(
        padding: const EdgeInsets.all(8),
        child: FutureBuilder<dynamic>(
          future: futureData,
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(
                child: SizedBox(
                  width: 24,
                  height: 24,
                  child: CircularProgressIndicator.adaptive(),
                ),
              );
            }

            if (snapshot.hasError) {
              return Center(
                child: Text(
                  'Error',
                  style: TextStyle(color: Colors.red.shade700, fontSize: 12),
                ),
              );
            }

            // Get data from snapshot
            var data = snapshot.data;
            double value = 0;

            // Extract value from data
            if (data != null) {
              if (data is List && data.isNotEmpty && data[0] is Map) {
                data[0].forEach((key, val) {
                  if (val is num) {
                    value = val.toDouble();
                  }
                });
              }
            }

            // Format value based on title
            String displayValue = value.toString();
            if (title.contains('Rate')) {
              // Convert to percentage for rates
              value = value * 100;
              displayValue = '${value.toStringAsFixed(1)}%';
            } else if (title.contains('Spend')) {
              // Format with K suffix for large numbers
              if (value >= 1000) {
                displayValue = '${(value / 1000).toStringAsFixed(1)}K';
              } else {
                displayValue = value.toStringAsFixed(0);
              }
            } else {
              // Format as integer for counts
              if (value >= 1000) {
                displayValue = '${(value / 1000).toStringAsFixed(1)}K';
              } else {
                displayValue = value.toStringAsFixed(0);
              }
            }

            // Choose icon based on title
            IconData icon;
            Color iconColor;
            if (title.contains('Spend')) {
              icon = Icons.attach_money;
              iconColor = Colors.green;
            } else if (title.contains('Sent')) {
              icon = Icons.send;
              iconColor = Colors.blue;
            } else if (title.contains('Delivered')) {
              icon = Icons.mark_email_read;
              iconColor = Colors.orange;
            } else if (title.contains('Rate')) {
              icon = Icons.trending_up;
              iconColor = Colors.purple;
            } else {
              icon = Icons.analytics;
              iconColor = Colors.blue;
            }

            return Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Icon(icon, size: 28, color: iconColor),
                Expanded(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        displayValue,
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: iconColor,
                        ),
                      ),
                      Text(
                        title,
                        style: TextStyle(
                          fontSize: 12,
                          color: Theme.of(context).textTheme.bodyMedium?.color,
                        ),
                        textAlign: TextAlign.right,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Campaign Analytics'),
        backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
        foregroundColor: Theme.of(context).appBarTheme.foregroundColor,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
          ),
        ],
      ),
      drawer: const MyDrawer(),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : errorMessage != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'Error: $errorMessage',
                        style: TextStyle(color: Colors.red.shade700),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _loadData,
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                )
              : RefreshIndicator(
                  onRefresh: _loadData,
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(12),
                    physics: const AlwaysScrollableScrollPhysics(),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        // Metric cards in a grid with better sizing
                        Wrap(
                          spacing: 8,
                          runSpacing: 8,
                          children: [
                            SizedBox(
                              width: MediaQuery.of(context).size.width / 2 - 16,
                              height: 120,
                              child: buildMetricCard(
                                  'Total Campaign Spend', totalSpend),
                            ),
                            SizedBox(
                              width: MediaQuery.of(context).size.width / 2 - 16,
                              height: 120,
                              child: buildMetricCard('Messages Sent', nbSent),
                            ),
                            SizedBox(
                              width: MediaQuery.of(context).size.width / 2 - 16,
                              height: 120,
                              child: buildMetricCard(
                                  'Messages Delivered', totalNbDeliv),
                            ),
                            SizedBox(
                              width: MediaQuery.of(context).size.width / 2 - 16,
                              height: 120,
                              child: buildMetricCard(
                                  'Delivery Rate', deliveryRate),
                            ),
                            SizedBox(
                              width: MediaQuery.of(context).size.width / 2 - 16,
                              height: 120,
                              child: buildMetricCard('Open Rate', openRate),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        // Charts
                        buildChart(
                            'Nb Sent & Nb Deliv per Campaign', campaignStats),
                        buildChart('Spending by Gender', spendByGender),
                      ],
                    ),
                  ),
                ),
    );
  }
}
