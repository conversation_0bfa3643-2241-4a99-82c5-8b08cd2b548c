import 'package:flutter/material.dart';
import 'package:comarkapp/dashboard_page1.dart';
import 'package:comarkapp/dashboard_page2.dart';
import 'package:comarkapp/auth_service.dart';
import 'package:comarkapp/login_page.dart';

class MyDrawer extends StatelessWidget {
  const MyDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          DrawerHeader(
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const CircleAvatar(
                  radius: 30,
                  backgroundColor: Colors.white,
                  child: Icon(
                    Icons.person,
                    size: 40,
                    color: Colors.blue,
                  ),
                ),
                const SizedBox(height: 10),
                const Text(
                  'CoMark App',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'Marketing Analytics',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.8),
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          ListTile(
            leading: const Icon(Icons.dashboard),
            title: const Text('Dashboard 1'),
            onTap: () {
              // Close the drawer
              Navigator.pop(context);

              // Navigate to Dashboard 1 if not already there
              if (context.widget.runtimeType != DashboardPage1) {
                Navigator.of(context).pushReplacementNamed('/dashboard1');
              }
            },
          ),
          ListTile(
            leading: const Icon(Icons.analytics),
            title: const Text('Dashboard 2'),
            onTap: () {
              // Close the drawer
              Navigator.pop(context);

              // Navigate to Dashboard 2 if not already there
              if (context.widget.runtimeType != DashboardPage2) {
                Navigator.of(context).pushReplacementNamed('/dashboard2');
              }
            },
          ),
          const Divider(),
          ListTile(
            leading: const Icon(Icons.settings),
            title: const Text('Settings'),
            onTap: () {
              Navigator.pop(context);
              Navigator.of(context).pushNamed('/settings');
            },
          ),
          ListTile(
            leading: const Icon(Icons.info),
            title: const Text('About Us'),
            onTap: () {
              Navigator.pop(context);
              Navigator.of(context).pushNamed('/about');
            },
          ),
          const Divider(),
          ListTile(
            leading: const Icon(Icons.logout, color: Colors.red),
            title: const Text('Logout', style: TextStyle(color: Colors.red)),
            onTap: () {
              // Close the drawer first
              Navigator.pop(context);

              // Perform logout
              try {
                final authService = AuthService();
                authService.logout();
                debugPrint("Logout initiated");
              } catch (e) {
                debugPrint("Exception during logout: $e");
              }

              // Navigate directly to login page
              Navigator.of(context).pushAndRemoveUntil(
                MaterialPageRoute(builder: (context) => const LoginPage()),
                (route) => false,
              );
            },
          ),
        ],
      ),
    );
  }
}
