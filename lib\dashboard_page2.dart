import 'package:comarkapp/dashboard_service.dart';
import 'package:comarkapp/my_drawer.dart';
import 'package:comarkapp/stacked_bar_chart.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'dart:math' as math;

class DashboardPage2 extends StatefulWidget {
  const DashboardPage2({super.key});

  @override
  State<DashboardPage2> createState() => _DashboardPage2State();
}

class _DashboardPage2State extends State<DashboardPage2> {
  final service = DashboardService();
  bool isLoading = true;
  String? errorMessage;

  // For custom tooltips
  OverlayEntry? _tooltipOverlayEntry;

  late Future<dynamic> openRateByCampaign;
  late Future<dynamic> deliveryRateByGender;
  late Future<dynamic> spendByAge;
  late Future<dynamic> viewsByDayAndAge;
  late Future<dynamic> viewsByTimeAndAge;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    // Clean up any tooltips when the widget is disposed
    if (_tooltipOverlayEntry != null) {
      _tooltipOverlayEntry!.remove();
      _tooltipOverlayEntry = null;
    }
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      isLoading = true;
      errorMessage = null;
    });

    try {
      openRateByCampaign = service.getOpenRateByCampaign();
      deliveryRateByGender = service.getDeliveryRateByGender();
      spendByAge = service.getNbSpendByAge();
      viewsByDayAndAge = service.getTODEOperAgeandDOWEO();
      viewsByTimeAndAge = service.getTODEOAGE();

      await Future.wait([
        openRateByCampaign,
        deliveryRateByGender,
        spendByAge,
        viewsByDayAndAge,
        viewsByTimeAndAge,
      ], eagerError: true);
    } catch (e) {
      debugPrint('Error loading data: $e');
      setState(() => errorMessage = e.toString());
    } finally {
      setState(() => isLoading = false);
    }
  }

  List<Map<String, dynamic>> transformApiData(dynamic data, String title) {
    debugPrint('Transforming data for $title: $data');

    if (data == null) {
      return [];
    }

    if (data is List && data.isNotEmpty && data.first is Map) {
      if (data.first.containsKey('x') &&
          (data.first.containsKey('y') || data.first.containsKey('value'))) {
        return List<Map<String, dynamic>>.from(data);
      }
    }

    List<Map<String, dynamic>> result = [];

    try {
      if (title == 'Open Rate By Campaign' && data is List) {
        Map<String, double> campaignRates = {};
        for (var item in data) {
          if (item is Map) {
            String campaignName = '';
            double rate = 0;
            item.forEach((key, val) {
              if (key.toString().contains('CAPTION') && val != null) {
                campaignName = val.toString().trim();
              } else if (val is num) {
                rate = val.toDouble();
              }
            });
            if (campaignName.isNotEmpty &&
                campaignName != 'null' &&
                !campaignName.toLowerCase().contains('unknown')) {
              if (rate <= 1) rate = rate * 100;
              campaignRates[campaignName] = rate;
            }
          }
        }
        int index = 0;
        List<String> sortedCampaigns = campaignRates.keys.toList()..sort();
        for (var campaign in sortedCampaigns) {
          result.add({
            'x': index,
            'y': campaignRates[campaign]!,
            'label': campaign,
            'color': Colors.blue,
          });
          index++;
        }
      } else if (title == 'Delivery Rate By Gender' && data is List) {
        Map<String, double> genderRates = {};
        for (var item in data) {
          if (item is Map) {
            String gender = '';
            double rate = 0;
            item.forEach((key, val) {
              if (key.toString().contains('CAPTION') && val != null) {
                gender = val.toString().trim();
              } else if (val is num) {
                rate = val.toDouble();
              }
            });
            if ((gender == 'Male' || gender == 'Female') && gender != 'null') {
              if (rate <= 1) rate = rate * 100;
              genderRates[gender] = rate;
            }
          }
        }
        int index = 0;
        List<String> sortedGenders = ['Male', 'Female']
            .where((g) => genderRates.containsKey(g))
            .toList();
        for (var gender in sortedGenders) {
          Color barColor = gender == 'Male'
              ? const Color(0xFF2196F3)
              : const Color(0xFFE91E63);
          result.add({
            'x': index,
            'y': genderRates[gender]!,
            'label': gender,
            'color': barColor,
            'isGenderChart': true,
          });
          index++;
        }
      } else if (title == 'Spend By Age Group' && data is List) {
        Map<String, double> ageSpends = {};
        double totalSpend = 0;

        // Process the data from the API
        for (var item in data) {
          if (item is Map) {
            String ageGroup = '';
            double spend = 0;

            // Extract age group and spend value from the API response
            item.forEach((key, val) {
              if (key
                      .toString()
                      .contains('[Age Bands].[Age].[Age].[MEMBER_CAPTION]') &&
                  val != null &&
                  val is String) {
                ageGroup = val.toString().trim();
              } else if (key.toString().contains('[Measures].[Spend]') &&
                  val != null &&
                  val is num) {
                spend = val.toDouble();
              }
            });

            // Skip empty, null, or unknown age groups
            if (ageGroup.isNotEmpty &&
                ageGroup != 'null' &&
                ageGroup != '{}' &&
                !ageGroup.toLowerCase().contains('unknown')) {
              ageSpends[ageGroup] = spend;
              totalSpend += spend;
            }
          }
        }

        // If no data was found, use default values (should not happen with proper API)
        if (ageSpends.isEmpty) {
          debugPrint('No valid age spend data found, using defaults');
          ageSpends = {
            '16-24': 38817,
            '25-34': 129476,
            '35-44': 104646,
            '45-54': 96454,
            '55-64': 31172,
          };
          totalSpend = ageSpends.values.fold(0, (sum, value) => sum + value);
        }

        // Define colors for each age group to match the image
        final Map<String, Color> ageGroupColors = {
          '16-24': const Color(0xFFFFEB3B), // Yellow
          '25-34': const Color(0xFF9C27B0), // Purple
          '35-44': const Color(0xFF4CAF50), // Green
          '45-54': const Color(0xFFFF5722), // Orange/Red
          '55-64': const Color(0xFF2196F3), // Blue
          '65-74': const Color(0xFF607D8B), // Blue Grey
          '75+': const Color(0xFF795548), // Brown
        };

        // Convert to percentage and add to result
        int index = 0;
        for (var entry in ageSpends.entries) {
          final percentage = (entry.value / totalSpend) * 100;
          result.add({
            'x': index,
            'y': entry.value,
            'label': entry.key,
            'percentage': percentage,
            'color': ageGroupColors[entry.key] ?? Colors.grey,
          });
          index++;
        }
      } else if (title == 'Unique Views by Day of Week and Age' &&
          data is List) {
        // Define days of week in correct order (1 = Sunday, 2 = Monday, etc.)
        // Using shorter names to fit better on the x-axis
        List<String> daysOfWeek = [
          'Sunday',
          'Monday',
          'Tuesday',
          'Wednesday',
          'Thursday',
          'Friday',
          'Saturday'
        ];

        // Shorter display names for x-axis
        List<String> shortDayNames = [
          'Sunday',
          'Monday',
          'Tuesday',
          'Wednes.',
          'Thursday',
          'Friday',
          'Saturday'
        ];

        // Map to store data for each day and age group
        Map<String, Map<String, double>> dayAgeViews = {};

        // Initialize the map with all days
        for (var day in daysOfWeek) {
          dayAgeViews[day] = {};
        }

        // Track all age groups
        Set<String> ageGroups = {};

        // Process the data from the API
        for (var item in data) {
          if (item is Map) {
            // Get the age group
            var ageGroup = item['[Age Bands].[Age].[Age].[MEMBER_CAPTION]'];

            // Skip the total row (which has an empty object as age group)
            if (ageGroup is Map && ageGroup.isEmpty) {
              continue;
            }

            // Convert age group to string
            String ageGroupStr = ageGroup.toString();

            // Add to our set of age groups if it's not empty
            if (ageGroupStr.isNotEmpty &&
                ageGroupStr != 'null' &&
                ageGroupStr != '{}') {
              ageGroups.add(ageGroupStr);
            }

            // Process each day of the week
            for (int i = 1; i <= 7; i++) {
              // Get the view count for this day
              var viewCount =
                  item['[Day Of Week Email Opened].[DayWEO].&[$i.]'];

              // Skip if no data
              if (viewCount == null || viewCount is! num) {
                continue;
              }

              // Get the day name (index is 1-based in the API)
              String dayName = daysOfWeek[i - 1];

              // Add the data to our map
              if (ageGroupStr.isNotEmpty &&
                  ageGroupStr != 'null' &&
                  ageGroupStr != '{}') {
                dayAgeViews[dayName]![ageGroupStr] = viewCount.toDouble();
              }
            }
          }
        }

        // We don't need to sort age groups here as they're handled in _createStackItemsForAgeGroups

        // Create chart data
        int index = 0;
        for (var day in daysOfWeek) {
          // Skip days with no data
          if (dayAgeViews[day]!.isEmpty) {
            continue;
          }

          // Calculate total views for this day
          double totalViews =
              dayAgeViews[day]!.values.fold(0, (sum, views) => sum + views);

          // Create detailed tooltip label - just show the current day's data
          String detailedLabel = day;

          // Add to result
          result.add({
            'x': index,
            'y': totalViews,
            'label': shortDayNames[
                daysOfWeek.indexOf(day)], // Use shorter name for display
            'displayLabel': day, // Full day name for tooltip
            'detailedLabel': detailedLabel,
            'color': Colors.green, // Will be overridden by the stacked items
            'ageData': dayAgeViews[day],
          });
          index++;
        }
      } else if (title == 'Unique Views by Time of Day and Age' &&
          data is List) {
        // Define time of day labels
        List<String> timeOfDayLabels = [
          '1. Early Morning',
          '2. Morning',
          '3. Afternoon',
          '4. Evening'
        ];

        // Map to store data for each time period and age group
        Map<String, Map<String, double>> timeAgeViews = {};

        // Initialize the map with all time periods
        for (var time in timeOfDayLabels) {
          timeAgeViews[time] = {};
        }

        // Track all age groups
        Set<String> ageGroups = {};

        // Track total views for each time period (for the line chart)
        Map<String, double> totalViewsByTime = {};
        for (var time in timeOfDayLabels) {
          totalViewsByTime[time] = 0;
        }

        // Process the data from the API
        for (var item in data) {
          if (item is Map) {
            // Get the age group
            var ageGroup = item['[Age Bands].[Age].[Age].[MEMBER_CAPTION]'];

            // Skip the total row (which has an empty object as age group)
            if (ageGroup is Map && ageGroup.isEmpty) {
              continue;
            }

            // Convert age group to string
            String ageGroupStr = ageGroup.toString();

            // Add to our set of age groups if it's not empty
            if (ageGroupStr.isNotEmpty &&
                ageGroupStr != 'null' &&
                ageGroupStr != '{}') {
              ageGroups.add(ageGroupStr);
            }

            // Process each time of day
            for (int i = 1; i <= 4; i++) {
              // Get the view count for this time period
              var viewCount =
                  item['[Time Of Day Email Opened].[TimeDEO].&[$i.]'];

              // Skip if no data
              if (viewCount == null || viewCount is! num) {
                continue;
              }

              // Get the time period name (index is 1-based in the API)
              String timeName = timeOfDayLabels[i - 1];

              // Add the data to our map
              if (ageGroupStr.isNotEmpty &&
                  ageGroupStr != 'null' &&
                  ageGroupStr != '{}') {
                timeAgeViews[timeName]![ageGroupStr] = viewCount.toDouble();

                // Add to total views for this time period
                totalViewsByTime[timeName] =
                    (totalViewsByTime[timeName] ?? 0) + viewCount.toDouble();
              }
            }
          }
        }

        // Sort the age groups for consistent display
        // We don't need to store this since it's just for debugging
        // ageGroups.toList().sort();

        // Create chart data for each time period
        for (int i = 0; i < timeOfDayLabels.length; i++) {
          String time = timeOfDayLabels[i];

          // Create a map of age groups to view counts for this time period
          Map<String, double> ageData = timeAgeViews[time] ?? {};

          // Add to result
          result.add({
            'x': i,
            'y':
                totalViewsByTime[time] ?? 0, // Total views for this time period
            'label': time,
            'ageData': ageData,
            'isTimePeriod': true,
          });
        }

        // Add a separate dataset for the line chart (total views)
        List<Map<String, dynamic>> lineDataset = [];
        for (int i = 0; i < timeOfDayLabels.length; i++) {
          String time = timeOfDayLabels[i];
          lineDataset.add({
            'x': i,
            'y': totalViewsByTime[time] ?? 0,
          });
        }

        // Add the line dataset to the result
        if (result.isNotEmpty) {
          result[0]['lineDataset'] = lineDataset;
        }
      } else if (data is List) {
        int index = 0;
        for (var item in data) {
          if (item is Map) {
            String label = '';
            double value = 0;
            item.forEach((key, val) {
              if (key.toString().contains('CAPTION') && val != null) {
                label = val.toString();
              } else if (val is num) {
                value = val.toDouble();
              }
            });
            if (label.isNotEmpty && label != 'null') {
              result.add({
                'x': index,
                'y': value,
                'label': label,
              });
              index++;
            }
          }
        }
      }
    } catch (e) {
      debugPrint('Error transforming data: $e');
    }

    debugPrint('Transformed data: $result');
    return result;
  }

  Widget buildBarChartFromData(
      List<Map<String, dynamic>> chartData, String title) {
    if (chartData.isEmpty) {
      return const Center(
        child: Text(
          'No data available',
          style: TextStyle(color: Colors.grey),
        ),
      );
    }

    final double maxY = chartData.fold(
        0.0, (double max, item) => math.max(max, (item['y'] ?? 0).toDouble()));
    bool manyBars = chartData.length > 10;

    try {
      return BarChart(
        swapAnimationDuration: const Duration(milliseconds: 800),
        swapAnimationCurve: Curves.easeInOutCubic,
        BarChartData(
          alignment: manyBars
              ? BarChartAlignment.start
              : BarChartAlignment.spaceAround,
          maxY: maxY * 1.2,
          minY: 0,
          barTouchData: BarTouchData(
            enabled: true,
            handleBuiltInTouches: title !=
                'Unique Views by Day of Week and Age', // Disable built-in touches for our custom chart
            touchCallback:
                (FlTouchEvent event, BarTouchResponse? touchResponse) {
              // Custom touch handling for the stacked bar chart
              if (title == 'Unique Views by Day of Week and Age' &&
                  touchResponse != null &&
                  touchResponse.spot != null &&
                  event is FlTapUpEvent) {
                // Get the bar that was touched
                final barIndex = touchResponse.spot!.touchedBarGroupIndex;
                final stackItemIndex =
                    touchResponse.spot!.touchedStackItemIndex;

                // Get the data for this bar
                if (barIndex >= 0 && barIndex < chartData.length) {
                  final barData = chartData[barIndex];
                  final dayName = barData['displayLabel'] ?? barData['label'];
                  final ageData = barData['ageData'] as Map<String, double>?;

                  if (ageData != null) {
                    // Get the age group and value for the specific stack item that was touched
                    final sortedAges = ageData.keys.toList()..sort();

                    if (stackItemIndex >= 0 &&
                        stackItemIndex < sortedAges.length) {
                      final ageGroup = sortedAges[stackItemIndex];
                      final value = ageData[ageGroup]!.toInt();

                      // Instead of using showDialog, we'll use an overlay to avoid the black screen issue
                      // First, dismiss any existing tooltips
                      if (_tooltipOverlayEntry != null) {
                        _tooltipOverlayEntry!.remove();
                        _tooltipOverlayEntry = null;
                      }

                      // Create a new tooltip overlay
                      _tooltipOverlayEntry = OverlayEntry(
                        builder: (context) {
                          return Stack(
                            children: [
                              Positioned(
                                // Position near the touch point (this is approximate)
                                top:
                                    MediaQuery.of(context).size.height / 2 - 50,
                                left:
                                    MediaQuery.of(context).size.width / 2 - 100,
                                child: Material(
                                  color: Colors.transparent,
                                  child: Container(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 8, vertical: 6),
                                    decoration: BoxDecoration(
                                      color: const Color(
                                          0xFF4A148C), // Deep purple background
                                      borderRadius: BorderRadius.circular(
                                          0), // No border radius
                                    ),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Text(
                                          '$dayName',
                                          style: const TextStyle(
                                            color: Colors.white,
                                            fontWeight: FontWeight.bold,
                                            fontSize: 14,
                                          ),
                                        ),
                                        const SizedBox(height: 2),
                                        Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Container(
                                              width: 10,
                                              height: 10,
                                              color: Colors.white,
                                            ),
                                            const SizedBox(width: 4),
                                            Text(
                                              'Unique Views by Day of Week and Age: $value',
                                              style: const TextStyle(
                                                color: Colors.white,
                                                fontSize: 11,
                                                fontWeight: FontWeight.normal,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          );
                        },
                      );

                      // Show the tooltip
                      Overlay.of(context).insert(_tooltipOverlayEntry!);

                      // Auto-dismiss after 2 seconds
                      Future.delayed(const Duration(seconds: 2), () {
                        if (_tooltipOverlayEntry != null) {
                          _tooltipOverlayEntry!.remove();
                          _tooltipOverlayEntry = null;
                        }
                      });
                    }
                  }
                }
              }
            },
            touchTooltipData: BarTouchTooltipData(
              tooltipBgColor: Colors.blueGrey.shade800,
              getTooltipItem: (group, groupIndex, rod, rodIndex) {
                // Don't show tooltips for our custom chart
                if (title == 'Unique Views by Day of Week and Age') {
                  return null;
                }

                final item = chartData.firstWhere(
                  (element) => (element['x'] ?? 0) == group.x,
                  orElse: () => {'label': group.x.toString(), 'y': rod.toY},
                );
                final label = item['label']?.toString() ?? group.x.toString();
                final value = rod.toY;
                String valueText;
                if (title.contains('Rate')) {
                  valueText = '${value.toStringAsFixed(1)}%';
                } else if (title.contains('Spend')) {
                  valueText = '\$${value.toStringAsFixed(0)}';
                } else {
                  valueText = value.toStringAsFixed(0);
                }

                if (item.containsKey('detailedLabel')) {
                  return BarTooltipItem(
                    item['detailedLabel'].toString(),
                    const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 10,
                    ),
                  );
                } else {
                  return BarTooltipItem(
                    '$label\n$valueText',
                    const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  );
                }
              },
            ),
          ),
          titlesData: FlTitlesData(
            show: true,
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                getTitlesWidget: (value, meta) {
                  final index = value.toInt();
                  final item = chartData.firstWhere(
                    (element) => (element['x'] ?? 0) == index,
                    orElse: () => {'label': index.toString()},
                  );
                  final label = item['label']?.toString() ?? index.toString();
                  if (title == 'Unique Views by Day of Week and Age') {
                    // Custom x-axis labels for this chart
                    return Padding(
                      padding: const EdgeInsets.only(top: 8.0),
                      child: Text(
                        label,
                        style: const TextStyle(
                          fontSize: 9,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    );
                  } else if (manyBars) {
                    return Padding(
                      padding: const EdgeInsets.only(top: 8.0),
                      child: RotatedBox(
                        quarterTurns: 3,
                        child: Text(
                          label,
                          style: const TextStyle(
                            fontSize: 8,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.right,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    );
                  } else {
                    final isGenderChart = title == 'Delivery Rate By Gender';
                    return Padding(
                      padding: const EdgeInsets.only(top: 8.0),
                      child: isGenderChart
                          ? Text(
                              label,
                              style: const TextStyle(
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                                color: Colors.black87,
                              ),
                              textAlign: TextAlign.center,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            )
                          : Container(
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(4),
                              ),
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 4, vertical: 2),
                              child: Text(
                                label,
                                style: const TextStyle(
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                ),
                                textAlign: TextAlign.center,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                    );
                  }
                },
                reservedSize: manyBars ? 60 : 20,
              ),
            ),
            leftTitles: AxisTitles(
              axisNameWidget: title == 'Unique Views by Day of Week and Age'
                  ? const Text(
                      'Number of Opens',
                      style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                        color: Colors.grey,
                      ),
                    )
                  : null,
              axisNameSize:
                  title == 'Unique Views by Day of Week and Age' ? 20 : 0,
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize:
                    title == 'Unique Views by Day of Week and Age' ? 50 : 40,
                getTitlesWidget: (value, meta) {
                  String text;
                  if (title == 'Unique Views by Day of Week and Age') {
                    // Format to match the image (e.g., "16,000")
                    if (value >= 1000) {
                      text = '${value.toInt()}';
                    } else {
                      text = value.toInt().toString();
                    }
                  } else if (title.contains('Rate')) {
                    text = '${value.toInt()}%';
                  } else if (value >= 1000) {
                    text = '${(value / 1000).toStringAsFixed(0)}K';
                  } else {
                    text = value.toInt().toString();
                  }
                  return Text(
                    text,
                    style: const TextStyle(
                      fontSize: 10,
                      color: Colors.grey,
                    ),
                    textAlign: TextAlign.right,
                  );
                },
              ),
            ),
            topTitles:
                const AxisTitles(sideTitles: SideTitles(showTitles: false)),
            rightTitles:
                const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          ),
          borderData: FlBorderData(show: false),
          gridData: FlGridData(
            show: true,
            drawVerticalLine: true,
            drawHorizontalLine: true,
            horizontalInterval: title == 'Unique Views by Day of Week and Age'
                ? 2000 // Fixed interval for this chart to match the image
                : (maxY > 10000
                    ? 5000
                    : (maxY > 1000 ? 500 : (maxY > 100 ? 20 : 10))),
            getDrawingHorizontalLine: (value) {
              return FlLine(
                color: Colors.grey.shade200,
                strokeWidth: 1,
                dashArray: [5, 5],
              );
            },
            getDrawingVerticalLine: (value) {
              return FlLine(
                color: Colors.grey.shade200,
                strokeWidth: 1,
                dashArray: [5, 5],
              );
            },
          ),
          barGroups: chartData.map<BarChartGroupData>((item) {
            final x = item['x'] ?? 0;
            final y = (item['y'] ?? 0).toDouble();
            Color barColor = item.containsKey('color') && item['color'] is Color
                ? item['color'] as Color
                : title.contains('Open Rate')
                    ? Colors.blue
                    : title.contains('Delivery Rate')
                        ? Colors.green
                        : title.contains('Spend')
                            ? Colors.orange
                            : title.contains('Views')
                                ? Colors.teal
                                : Colors.purple;
            return BarChartGroupData(
              x: x is int ? x : x.toInt(),
              barRods: [
                BarChartRodData(
                  toY: y,
                  color: title == 'Unique Views by Day of Week and Age'
                      ? Colors.teal
                      : barColor,
                  width: title == 'Unique Views by Day of Week and Age'
                      ? 30 // Wider bars for this chart to match the image
                      : (item['isGenderChart'] == true
                          ? 40
                          : (manyBars ? 8 : 16)),
                  borderRadius: item['isGenderChart'] == true
                      ? const BorderRadius.vertical(
                          top: Radius.circular(6), bottom: Radius.circular(0))
                      : BorderRadius.circular(6),
                  backDrawRodData: BackgroundBarChartRodData(
                    show: item['isGenderChart'] == true ? false : true,
                    toY: maxY * 1.1,
                    color: title == 'Unique Views by Day of Week and Age'
                        ? Colors.teal.withOpacity(0.1)
                        : Colors.grey.shade200,
                  ),
                  rodStackItems:
                      title == 'Unique Views by Day of Week and Age' &&
                              item.containsKey('ageData')
                          ? _createStackItemsForAgeGroups(
                              item['ageData'] as Map<String, double>, maxY)
                          : [],
                )
              ],
            );
          }).toList(),
        ),
      );
    } catch (e) {
      debugPrint('Error rendering bar chart: $e');
      return Center(
        child: Text(
          'Error rendering chart',
          style: TextStyle(color: Colors.red.shade700),
        ),
      );
    }
  }

  Widget buildChart(String title, Future<dynamic> futureData) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
                letterSpacing: 0.5,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 250,
              child: FutureBuilder<dynamic>(
                future: futureData,
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting &&
                      !snapshot.hasData) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const CircularProgressIndicator.adaptive(),
                          const SizedBox(height: 16),
                          Text(
                            'Loading data...',
                            style: TextStyle(
                              color: Colors.grey.shade600,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    );
                  }
                  if (snapshot.hasError) {
                    debugPrint('Error for $title: ${snapshot.error}');
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.error_outline,
                            color: Colors.red.shade400,
                            size: 40,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Error loading data',
                            style: TextStyle(
                              color: Colors.red.shade700,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Please try again later',
                            style: TextStyle(
                              color: Colors.grey.shade600,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    );
                  }
                  var data = snapshot.data;
                  debugPrint('Raw data for $title: $data');
                  var transformedData = transformApiData(data, title);
                  if (transformedData.isEmpty) {
                    debugPrint('No data available for $title');
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          if (title == 'Unique Views by Day of Week and Age')
                            Column(
                              children: [
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: List.generate(
                                    3,
                                    (index) => Container(
                                      margin: const EdgeInsets.symmetric(
                                          horizontal: 4),
                                      width: 8,
                                      height: 20 + (index * 10).toDouble(),
                                      decoration: BoxDecoration(
                                        color: Colors.grey.shade300,
                                        borderRadius: BorderRadius.circular(4),
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(height: 16),
                              ],
                            )
                          else
                            Icon(
                              Icons.bar_chart_outlined,
                              color: Colors.grey.shade400,
                              size: 40,
                            ),
                          const SizedBox(height: 16),
                          Text(
                            'No data available',
                            style: TextStyle(
                              color: Colors.grey.shade600,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    );
                  }
                  return AnimatedOpacity(
                    opacity: 1.0,
                    duration: const Duration(milliseconds: 500),
                    curve: Curves.easeIn,
                    child: title == 'Open Rate By Campaign'
                        ? buildLineChart(transformedData, title)
                        : title == 'Spend By Age Group'
                            ? buildPieChart(transformedData, title)
                            : title == 'Unique Views by Time of Day and Age'
                                ? buildMixedChart(transformedData, title)
                                : title == 'Unique Views by Day of Week and Age'
                                    ? Column(
                                        children: [
                                          // Legend for age groups - two rows to match image
                                          Padding(
                                            padding: const EdgeInsets.only(
                                                bottom: 8.0),
                                            child: Column(
                                              children: [
                                                // First row of legend
                                                Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.center,
                                                  children: [
                                                    _buildLegendItem(
                                                        'total',
                                                        const Color(
                                                            0xFF4CAF50)),
                                                    const SizedBox(width: 12),
                                                    _buildLegendItem(
                                                        '16-24',
                                                        const Color(
                                                            0xFFFFEB3B)),
                                                    const SizedBox(width: 12),
                                                    _buildLegendItem(
                                                        '25-34',
                                                        const Color(
                                                            0xFF2E7D32)),
                                                    const SizedBox(width: 12),
                                                    _buildLegendItem(
                                                        '35-44',
                                                        const Color(
                                                            0xFFFF80AB)),
                                                  ],
                                                ),
                                                const SizedBox(height: 4),
                                                // Second row of legend
                                                Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.center,
                                                  children: [
                                                    _buildLegendItem(
                                                        '45-54',
                                                        const Color(
                                                            0xFF3F51B5)),
                                                    const SizedBox(width: 12),
                                                    _buildLegendItem(
                                                        '55-64',
                                                        const Color(
                                                            0xFF00BCD4)),
                                                    const SizedBox(width: 12),
                                                    _buildLegendItem(
                                                        '65-74',
                                                        const Color(
                                                            0xFF4CAF50)),
                                                    const SizedBox(width: 12),
                                                    _buildLegendItem(
                                                        '75+',
                                                        const Color(
                                                            0xFF3F51B5)),
                                                  ],
                                                ),
                                              ],
                                            ),
                                          ),
                                          // The chart
                                          Expanded(
                                            child: buildBarChartFromData(
                                                transformedData, title),
                                          ),
                                        ],
                                      )
                                    : buildBarChartFromData(
                                        transformedData, title),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget buildMixedChart(List<Map<String, dynamic>> chartData, String title) {
    // Use our new StackedBarChart component
    return StackedBarChart(
      chartData: chartData,
      title: title,
    );
  }

  List<BarChartRodStackItem> _createStackItemsForAgeGroups(
      Map<String, double> ageData, double maxY) {
    if (ageData.isEmpty) return [];

    // Colors matching the image exactly
    final ageColors = {
      'total': const Color(0xFF4CAF50), // Green
      '16-24': const Color(0xFFFFEB3B), // Yellow
      '25-34': const Color(0xFF2E7D32), // Dark Green
      '35-44': const Color(0xFFFF80AB), // Pink
      '45-54': const Color(0xFF3F51B5), // Indigo
      '55-64': const Color(0xFF00BCD4), // Cyan
      '65-74': const Color(0xFF4CAF50), // Green
      '75+': const Color(0xFF3F51B5), // Indigo
    };
    final sortedAges = ageData.keys.toList()..sort();
    List<BarChartRodStackItem> stackItems = [];
    double fromY = 0;
    for (var age in sortedAges) {
      final value = ageData[age] ?? 0;
      final toY = fromY + value;
      stackItems.add(
        BarChartRodStackItem(
          fromY,
          toY,
          ageColors[age] ?? Colors.teal,
        ),
      );
      fromY = toY;
    }
    return stackItems;
  }

  // Helper method to build legend items
  Widget _buildLegendItem(String label, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 8,
          height: 8,
          decoration: BoxDecoration(
            color: color,
            border: Border.all(color: Colors.grey.shade300, width: 0.5),
          ),
        ),
        const SizedBox(width: 2),
        Text(
          label,
          style: const TextStyle(
            fontSize: 7,
            fontWeight: FontWeight.normal,
            color: Colors.black87,
          ),
        ),
      ],
    );
  }

  Widget buildPieChart(List<Map<String, dynamic>> chartData, String title) {
    if (chartData.isEmpty) {
      return const Center(
        child: Text(
          'No data available',
          style: TextStyle(color: Colors.grey),
        ),
      );
    }

    try {
      // Sort data by value in descending order for better visualization
      chartData.sort((a, b) => (b['y'] as num).compareTo(a['y'] as num));

      final sections = <PieChartSectionData>[];

      // Define colors for age groups - matching the Angular implementation
      final Map<String, Color> ageGroupColors = {
        '16-24': const Color(0xFFFF6384), // Pink/Red
        '25-34': const Color(0xFF36A2EB), // Blue
        '35-44': const Color(0xFF2ECC71), // Green
        '45-54': const Color(0xFF8E44AD), // Purple
        '55-64': const Color(0xFFF1C40F), // Yellow
        '65-74': const Color(0xFFE67E22), // Orange
        '75+': const Color(0xFF3498DB), // Light Blue
        'unknown':
            Colors.grey.shade400, // Grey for unknown (will be filtered out)
      };

      // Create a list to store legend items
      final List<Map<String, dynamic>> legendItems = [];

      // Process chart data
      for (var item in chartData) {
        final value = (item['y'] ?? 0).toDouble();
        final label = item['label'] as String;

        // Skip 'unknown' gender as per user preference
        if (label.toLowerCase() == 'unknown') {
          continue;
        }

        // Add to legend items without percentage
        legendItems.add({
          'label': label,
          'value': value,
          'color': ageGroupColors[label] ?? Colors.grey,
        });

        // Create pie section
        sections.add(
          PieChartSectionData(
            color: ageGroupColors[label] ?? Colors.grey,
            value: value,
            title: '', // No title on the sections to match web version
            radius: 110,
            titleStyle: const TextStyle(
              fontSize: 0, // Zero size to hide text
              color: Colors.transparent,
            ),
            showTitle: false, // Don't show titles on the chart sections
          ),
        );
      }

      return LayoutBuilder(
        builder: (context, constraints) {
          // Use constraints for responsive sizing if needed

          return Column(
            children: [
              // Title for the chart
              // No title needed here as it's already in the card

              // Chart container with legend on the left side
              SizedBox(
                height: 250, // Perfect height to match the image
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    // Doughnut chart first (on the left) - matching Angular implementation

                    // Doughnut chart - no white box or shadow
                    Expanded(
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          AspectRatio(
                            aspectRatio: 1.0, // Perfect circle
                            child: Padding(
                              padding: EdgeInsets
                                  .zero, // No padding for maximum size
                              child: PieChart(
                                PieChartData(
                                  sections: sections,
                                  centerSpaceRadius:
                                      20, // Proper hole size to match the image
                                  sectionsSpace:
                                      0, // No spaces between sections
                                  pieTouchData: PieTouchData(
                                    enabled: true,
                                    touchCallback:
                                        (FlTouchEvent event, pieTouchResponse) {
                                      // Show tooltip on touch
                                      if (pieTouchResponse != null &&
                                          pieTouchResponse.touchedSection !=
                                              null &&
                                          event is FlTapUpEvent) {
                                        final touchedIndex = pieTouchResponse
                                            .touchedSection!
                                            .touchedSectionIndex;
                                        if (touchedIndex >= 0 &&
                                            touchedIndex < legendItems.length) {
                                          final item =
                                              legendItems[touchedIndex];
                                          final label = item['label'] as String;
                                          final value = item['value'] as double;

                                          // Create an overlay with a custom tooltip
                                          showDialog(
                                            context: context,
                                            barrierColor: Colors.transparent,
                                            barrierDismissible:
                                                true, // Dismiss when tapped outside
                                            // Auto-dismiss after 2 seconds
                                            builder: (BuildContext context) {
                                              Future.delayed(
                                                  const Duration(seconds: 2),
                                                  () {
                                                Navigator.of(context).pop();
                                              });
                                              return Stack(
                                                children: [
                                                  // Positioned tooltip at the center of the screen
                                                  // In a real app, you would calculate the position based on touch coordinates
                                                  Positioned(
                                                    top: MediaQuery.of(context)
                                                                .size
                                                                .height /
                                                            2 -
                                                        30,
                                                    left: MediaQuery.of(context)
                                                                .size
                                                                .width /
                                                            2 -
                                                        100,
                                                    child: Material(
                                                      color: Colors.transparent,
                                                      child: Container(
                                                        padding:
                                                            const EdgeInsets
                                                                .symmetric(
                                                                horizontal: 8,
                                                                vertical: 6),
                                                        decoration:
                                                            BoxDecoration(
                                                          color: const Color(
                                                              0xFF4A148C), // Deep purple background
                                                          borderRadius:
                                                              BorderRadius.circular(
                                                                  0), // No border radius
                                                        ),
                                                        child: Column(
                                                          crossAxisAlignment:
                                                              CrossAxisAlignment
                                                                  .start,
                                                          mainAxisSize:
                                                              MainAxisSize.min,
                                                          children: [
                                                            Text(
                                                              label,
                                                              style:
                                                                  const TextStyle(
                                                                color: Colors
                                                                    .white,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .bold,
                                                                fontSize: 14,
                                                              ),
                                                            ),
                                                            const SizedBox(
                                                                height: 2),
                                                            Row(
                                                              mainAxisSize:
                                                                  MainAxisSize
                                                                      .min,
                                                              children: [
                                                                Container(
                                                                  width: 10,
                                                                  height: 10,
                                                                  color: Colors
                                                                      .white,
                                                                ),
                                                                const SizedBox(
                                                                    width: 4),
                                                                Text(
                                                                  'Spend by Age Group: ${value.toInt()}',
                                                                  style:
                                                                      const TextStyle(
                                                                    color: Colors
                                                                        .white,
                                                                    fontSize:
                                                                        11,
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .normal,
                                                                  ),
                                                                ),
                                                              ],
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              );
                                            },
                                          );
                                        }
                                      }
                                    },
                                  ),
                                  borderData: FlBorderData(show: false),
                                ),
                                swapAnimationDuration:
                                    const Duration(milliseconds: 800),
                                swapAnimationCurve: Curves.easeInOutCubic,
                              ),
                            ),
                          ),

                          // We'll use the built-in tooltip functionality of fl_chart
                          // which matches the Angular Chart.js tooltip behavior
                        ],
                      ),
                    ),

                    // Right side legend - matching Angular implementation
                    Padding(
                      padding: const EdgeInsets.only(left: 12.0),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: legendItems.map((item) {
                          return Padding(
                            padding: const EdgeInsets.symmetric(vertical: 2.0),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Container(
                                  width: 12,
                                  height: 12,
                                  decoration: BoxDecoration(
                                    color: item['color'],
                                    shape: BoxShape.circle,
                                  ),
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  item['label'] as String,
                                  style: const TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          );
                        }).toList(),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          );
        },
      );
    } catch (e) {
      debugPrint('Error rendering pie chart: $e');
      return Center(
        child: Text(
          'Error rendering chart',
          style: TextStyle(color: Colors.red.shade700),
        ),
      );
    }
  }

  Widget buildLineChart(List<Map<String, dynamic>> chartData, String title) {
    if (chartData.isEmpty) {
      return const Center(
        child: Text(
          'No data available',
          style: TextStyle(color: Colors.grey),
        ),
      );
    }
    final double maxY = chartData.fold(
        0.0, (double max, item) => math.max(max, (item['y'] ?? 0).toDouble()));
    chartData.sort((a, b) => (a['x'] as int).compareTo(b['x'] as int));
    try {
      return LineChart(
        LineChartData(
          lineTouchData: LineTouchData(
            touchTooltipData: LineTouchTooltipData(
              tooltipBgColor: Colors.blueGrey.shade800,
              getTooltipItems: (List<LineBarSpot> touchedSpots) {
                return touchedSpots.map((spot) {
                  final item = chartData.firstWhere(
                    (element) => (element['x'] ?? 0) == spot.x.toInt(),
                    orElse: () => {'label': spot.x.toString(), 'y': spot.y},
                  );
                  final label = item['label']?.toString() ?? spot.x.toString();
                  final value = spot.y;
                  return LineTooltipItem(
                    '$label\n${value.toStringAsFixed(1)}%',
                    const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  );
                }).toList();
              },
            ),
          ),
          gridData: FlGridData(
            show: true,
            drawVerticalLine: true,
            horizontalInterval: 10,
            getDrawingHorizontalLine: (value) {
              return FlLine(
                color: Colors.grey.shade200,
                strokeWidth: 1,
                dashArray: [5, 5],
              );
            },
            getDrawingVerticalLine: (value) {
              return FlLine(
                color: Colors.grey.shade200,
                strokeWidth: 1,
                dashArray: [5, 5],
              );
            },
          ),
          titlesData: FlTitlesData(
            show: true,
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                getTitlesWidget: (value, meta) {
                  final index = value.toInt();
                  final item = chartData.firstWhere(
                    (element) => (element['x'] ?? 0) == index,
                    orElse: () => {'label': index.toString()},
                  );
                  final label = item['label']?.toString() ?? index.toString();
                  if (chartData.length > 10) {
                    return Padding(
                      padding: const EdgeInsets.only(top: 8.0),
                      child: RotatedBox(
                        quarterTurns: 3,
                        child: Text(
                          label,
                          style: const TextStyle(
                            fontSize: 8,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.right,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    );
                  } else {
                    return Padding(
                      padding: const EdgeInsets.only(top: 8.0),
                      child: Text(
                        label,
                        style: const TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    );
                  }
                },
                reservedSize: chartData.length > 10 ? 60 : 20,
              ),
            ),
            leftTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 40,
                getTitlesWidget: (value, meta) {
                  return Text(
                    '${value.toInt()}%',
                    style: const TextStyle(
                      fontSize: 10,
                      color: Colors.grey,
                    ),
                    textAlign: TextAlign.right,
                  );
                },
              ),
            ),
            topTitles:
                const AxisTitles(sideTitles: SideTitles(showTitles: false)),
            rightTitles:
                const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          ),
          borderData: FlBorderData(show: false),
          minX: 0,
          maxX: chartData.length - 1.0,
          minY: 0,
          maxY: math.max(100, maxY * 1.2),
          lineBarsData: [
            LineChartBarData(
              spots: chartData.map((point) {
                return FlSpot(
                  (point['x'] ?? 0).toDouble(),
                  (point['y'] ?? 0).toDouble(),
                );
              }).toList(),
              isCurved: true,
              color: Colors.blue,
              barWidth: 3,
              isStrokeCapRound: true,
              dotData: FlDotData(
                show: true,
                getDotPainter: (spot, percent, barData, index) {
                  return FlDotCirclePainter(
                    radius: 4,
                    color: Colors.blue,
                    strokeWidth: 2,
                    strokeColor: Colors.white,
                  );
                },
              ),
              belowBarData: BarAreaData(
                show: true,
                color: Colors.blue.withOpacity(0.2),
              ),
            ),
          ],
        ),
      );
    } catch (e) {
      debugPrint('Error rendering line chart: $e');
      return Center(
        child: Text(
          'Error rendering chart',
          style: TextStyle(color: Colors.red.shade700),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Dashboard Page 2'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
          ),
        ],
      ),
      drawer: const MyDrawer(),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : errorMessage != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'Error: $errorMessage',
                        style: TextStyle(color: Colors.red.shade700),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _loadData,
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                )
              : RefreshIndicator(
                  onRefresh: _loadData,
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(12),
                    physics: const AlwaysScrollableScrollPhysics(),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        buildChart('Open Rate By Campaign', openRateByCampaign),
                        buildChart(
                            'Delivery Rate By Gender', deliveryRateByGender),
                        buildChart('Spend By Age Group', spendByAge),
                        buildChart('Unique Views by Day of Week and Age',
                            viewsByDayAndAge),
                        buildChart('Unique Views by Time of Day and Age',
                            viewsByTimeAndAge),
                      ],
                    ),
                  ),
                ),
    );
  }
}
